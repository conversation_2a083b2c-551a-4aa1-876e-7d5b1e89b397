import React from "react";
import { useTranslation } from "react-i18next";
import { Alert, Platform, ScrollView, StyleSheet, View } from "react-native";
import { Appbar, Divider, List, Switch } from "react-native-paper";

import { spacing } from "@/constants/theme";
import { usePersistentStorage } from "@/hooks";
import { usePantryStore, useSettingsStore } from "@/stores";

export default function SettingsScreen() {
  const { t, i18n } = useTranslation();
  const { clearAllData } = usePersistentStorage();

  const {
    language,
    theme,
    notifications,
    setLanguage,
    setTheme,
    setNotifications,
  } = useSettingsStore();

  const { clearAllProducts } = usePantryStore();

  const handleLanguageChange = (newLanguage: string) => {
    setLanguage(newLanguage);
    i18n.changeLanguage(newLanguage);
  };

  const handleThemeChange = (newTheme: "light" | "dark" | "auto") => {
    setTheme(newTheme);
  };

  const handleNotificationsToggle = () => {
    setNotifications(!notifications);
  };

  const handleClearData = () => {
    Alert.alert(t("settings.clearData"), t("settings.clearDataConfirmation"), [
      {
        text: t("common.cancel"),
        style: "cancel",
      },
      {
        text: t("common.confirm"),
        style: "destructive",
        onPress: async () => {
          await clearAllData();
          clearAllProducts();
        },
      },
    ]);
  };

  return (
    <View style={styles.container}>
      <Appbar.Header style={styles.header}>
        <Appbar.Content title={t("settings.title")} />
      </Appbar.Header>

      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
      >
        <List.Section>
          <List.Subheader>{t("settings.language")}</List.Subheader>
          <List.Item
            title="English"
            left={() => <List.Icon icon="translate" />}
            right={() =>
              language === "en" ? <List.Icon icon="check" /> : null
            }
            onPress={() => handleLanguageChange("en")}
          />
        </List.Section>

        <Divider />

        <List.Section>
          <List.Subheader>{t("settings.theme")}</List.Subheader>
          <List.Item
            title="Light"
            left={() => <List.Icon icon="white-balance-sunny" />}
            right={() =>
              theme === "light" ? <List.Icon icon="check" /> : null
            }
            onPress={() => handleThemeChange("light")}
          />
          <List.Item
            title="Dark"
            left={() => <List.Icon icon="moon-waning-crescent" />}
            right={() => (theme === "dark" ? <List.Icon icon="check" /> : null)}
            onPress={() => handleThemeChange("dark")}
          />
          <List.Item
            title="Auto"
            left={() => <List.Icon icon="theme-light-dark" />}
            right={() => (theme === "auto" ? <List.Icon icon="check" /> : null)}
            onPress={() => handleThemeChange("auto")}
          />
        </List.Section>

        <Divider />

        <List.Section>
          <List.Subheader>{t("settings.notifications")}</List.Subheader>
          <List.Item
            title={t("settings.notifications")}
            left={() => <List.Icon icon="bell" />}
            right={() => (
              <Switch
                value={notifications}
                onValueChange={handleNotificationsToggle}
              />
            )}
          />
        </List.Section>

        <Divider />

        <List.Section>
          <List.Subheader>Data</List.Subheader>
          <List.Item
            title={t("settings.clearData")}
            description="Remove all products from pantry"
            left={() => <List.Icon icon="delete" />}
            onPress={handleClearData}
            titleStyle={{ color: "#F44336" }}
          />
        </List.Section>

        <Divider />

        <List.Section>
          <List.Subheader>{t("settings.about")}</List.Subheader>
          <List.Item
            title={t("settings.version")}
            description="1.0.0"
            left={() => <List.Icon icon="information" />}
          />
          <List.Item
            title="BarcodePantry"
            description="Smart pantry management with barcode scanning"
            left={() => <List.Icon icon="barcode" />}
          />
        </List.Section>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F7F7F7",
  },
  content: {
    flex: 1,
  },
  scrollContent: {
    paddingBottom: Platform.OS === "ios" ? 100 : 80,
  },
  header: {
    paddingHorizontal: spacing.sm,
    elevation: 2,
  },
});
