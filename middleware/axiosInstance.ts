import { buildApiAddress } from "@/utils/buildApiAddress";
import axios from "axios";

console.log("API:", buildApiAddress());

export const axiosInstance = axios.create({
  baseURL: buildApiAddress(),
  timeout: 10000,
  headers: {
    "Content-Type": "application/json",
    "User-Agent": "BarcodePantry/1.0.0",
  },
});

axiosInstance.interceptors.request.use(function (config) {
  console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
  return config;
});

axiosInstance.interceptors.response.use(
  function (response) {
    console.log(
      `✅ API Response: ${
        response.status
      } ${response.config.method?.toUpperCase()} ${response.config.url}`
    );
    return response;
  },
  function (error) {
    const status = error.response?.status;
    const url = error.config?.url;
    const method = error.config?.method?.toUpperCase();

    console.log(`❌ API Error: ${status} ${method} ${url}`);
    return Promise.reject(error);
  }
);

export default axiosInstance;
