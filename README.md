# 📱 BarcodePantry - Smart Pantry Management

> **MVP SaaS Application** - Smart pantry management with barcode scanning capabilities

BarcodePantry is a React Native mobile application that helps users manage their pantry inventory through barcode scanning. Built with Expo, it provides a seamless experience for tracking products, quantities, and expiry dates.

## 🎯 **MVP Features**

- ✅ **Barcode Scanning** - Add/remove products by scanning barcodes
- ✅ **Dual Scanner Modes** - Switch between ADD and REMOVE modes
- ✅ **Product Database Integration** - Automatic product info from Open Food Facts API
- ✅ **Manual Product Entry** - Add products manually when barcode isn't found
- ✅ **Local Storage** - Offline-first with AsyncStorage
- ✅ **Product Management** - Edit quantities, delete products, search & filter
- ✅ **Multi-language Support** - i18n ready (English implemented)
- ✅ **Modern UI** - Material Design 3 with React Native Paper

## 🚀 **Quick Start**

### Prerequisites

- Node.js 18+
- Expo CLI
- iOS Simulator / Android Emulator or physical device

### Installation

1. **Clone and install dependencies**

   ```bash
   git clone <repository-url>
   cd BarcodePantryv2
   npm install
   ```

2. **Start the development server**

   ```bash
   npx expo start
   ```

3. **Run on device/simulator**
   - Press `i` for iOS simulator
   - Press `a` for Android emulator
   - Scan QR code with Expo Go app for physical device

## 📱 **App Structure**

### Main Screens

- **Pantry** (`/`) - Product list with search, filter, and sorting
- **Scanner** (`/scanner`) - Barcode scanning with ADD/REMOVE modes
- **Settings** (`/settings`) - App configuration and data management
- **Add Product** (`/add-product`) - Manual product entry form
- **Product Details** (`/product/[id]`) - Individual product management

### Key Features

- **Smart Scanning**: Automatic product recognition via Open Food Facts API
- **Offline-First**: All data stored locally with AsyncStorage
- **Error Handling**: Comprehensive error management with user-friendly messages
- **Responsive Design**: Optimized for both iOS and Android

## 🏗️ **Architecture Overview**

### Project Structure

```
├── api/                 # API services (Open Food Facts)
├── app/                 # Expo Router screens
├── assets/              # Images, fonts, static resources
├── components/          # Reusable UI components
├── constants/           # Theme, colors, spacing
├── contexts/            # React contexts
├── enums/               # TypeScript enums
├── errors/              # Error handling classes
├── hooks/               # Custom React hooks
├── locales/             # i18n translations
├── middleware/          # Axios interceptors
├── stores/              # Zustand state management
├── types/               # TypeScript type definitions
├── utils/               # Helper functions
└── validations/         # Zod schemas
```

### Tech Stack

- **Framework**: React Native + Expo SDK 53
- **Navigation**: Expo Router (file-based routing)
- **UI Library**: React Native Paper (Material Design 3)
- **State Management**: Zustand
- **Forms**: React Hook Form + Zod validation
- **Storage**: AsyncStorage
- **API**: Axios with Open Food Facts
- **Camera**: Expo Camera + Barcode Scanner
- **i18n**: react-i18next
- **TypeScript**: Full type safety

### State Management (Zustand)

- **PantryStore**: Product CRUD operations, filtering, sorting
- **ScannerStore**: Scanner mode, permissions, scanning state
- **SettingsStore**: App preferences, theme, language

### Error Handling

- **Custom Error Classes**: Typed error handling for different scenarios
- **Error Hook**: `useErrorHandling` with toast notifications
- **Graceful Degradation**: Fallbacks for API failures

## 🎨 **Design System**

### Color Palette

- **Primary**: `#FEA405` (Orange)
- **Secondary**: `#B66B00` (Dark Orange)
- **Accent**: `#FFD477` (Light Orange)
- **Text**: `#2B2B2B` (Dark Gray)
- **Background**: `#F7F7F7` (Light Gray)

### Typography & Spacing

- **Material Design 3** typography scale
- **Consistent spacing** system (4px base unit)
- **Responsive layouts** for different screen sizes

## 📊 **MVP Success Metrics**

### Target Metrics (2 weeks)

- ✅ **5+ Active Users** - Testing for 7 consecutive days
- ✅ **30+ Products Added** - At least 5 manually added
- ✅ **2+ API Integrations** - Products identified via Open Food Facts
- ✅ **User Feedback** - 3+ user interviews completed

### Current Status

- **Development**: ✅ Complete
- **Testing**: 🔄 Ready for user testing
- **Deployment**: 🔄 Ready for EAS Build

## 🚀 **Deployment & Distribution**

### EAS Build Setup

```bash
# Install EAS CLI
npm install -g @expo/eas-cli

# Login to Expo
eas login

# Configure build
eas build:configure

# Build for development
eas build --platform all --profile development

# Build for production
eas build --platform all --profile production
```

### App Store Deployment

```bash
# Submit to app stores
eas submit --platform ios
eas submit --platform android
```

### Environment Configuration

Create `.env` file for production:

```env
EXPO_PUBLIC_API_URL=https://world.openfoodfacts.org/api/v0
EXPO_PUBLIC_APP_VERSION=1.0.0
```

## 🧪 **Testing Strategy**

### Manual Testing Checklist

- [ ] **Barcode Scanning**: Test with various product barcodes
- [ ] **API Integration**: Verify Open Food Facts responses
- [ ] **Offline Mode**: Test without internet connection
- [ ] **Data Persistence**: Verify local storage functionality
- [ ] **Error Handling**: Test network failures and invalid inputs
- [ ] **UI/UX**: Test on different screen sizes and orientations

### User Testing Plan

1. **Onboarding**: First-time user experience
2. **Core Workflow**: Add products via scanning and manual entry
3. **Product Management**: Edit, delete, search functionality
4. **Edge Cases**: Handle API failures, invalid barcodes
5. **Performance**: App responsiveness and loading times

## 🔮 **Future Development Roadmap**

### Phase 2 - Enhanced Features (4-6 weeks)

- [ ] **Cloud Sync** - Firebase/Supabase integration
- [ ] **Family Sharing** - Multi-user pantry access
- [ ] **Expiry Notifications** - Push notifications for expiring products
- [ ] **Shopping Lists** - Generate lists from low stock items
- [ ] **Recipe Integration** - Suggest recipes based on available ingredients
- [ ] **Barcode Generation** - Create custom barcodes for bulk items

### Phase 3 - Premium Features (8-12 weeks)

- [ ] **Analytics Dashboard** - Usage patterns and insights
- [ ] **Multiple Pantries** - Separate inventories (kitchen, garage, etc.)
- [ ] **Advanced Search** - Filter by expiry, category, nutritional info
- [ ] **Meal Planning** - Weekly meal plans with ingredient tracking
- [ ] **Price Tracking** - Monitor product prices over time
- [ ] **API Integrations** - Grocery store APIs, nutrition databases

### Phase 4 - Monetization (12+ weeks)

- [ ] **Subscription Model** - Premium features ($2.99/month)
- [ ] **Family Plans** - Multi-user subscriptions ($4.99/month)
- [ ] **Business Features** - Restaurant/cafe inventory management
- [ ] **White Label** - Customizable app for grocery stores
- [ ] **Affiliate Integration** - Grocery delivery partnerships

## 🛠️ **Development Guidelines**

### Code Standards

- **TypeScript**: Strict mode enabled
- **ESLint**: Expo configuration
- **Prettier**: Consistent code formatting
- **Conventional Commits**: Semantic commit messages

### Component Guidelines

- **Atomic Design**: Atoms → Molecules → Organisms → Templates
- **Reusability**: Components should be generic and configurable
- **Accessibility**: WCAG 2.1 AA compliance
- **Performance**: Optimize for 60fps animations

### State Management Rules

- **Zustand Stores**: Single responsibility principle
- **Local State**: Use for component-specific data only
- **Persistence**: Critical data must be stored locally
- **Error Boundaries**: Graceful error handling at store level

## 🐛 **Known Issues & Limitations**

### Current Limitations

- **API Dependency**: Limited to Open Food Facts database
- **Offline Scanning**: No offline barcode database
- **Image Storage**: No local image caching
- **Bulk Operations**: No batch add/remove functionality

### Planned Fixes

- **Multiple APIs**: Integrate additional product databases
- **Offline Database**: Local barcode cache for common products
- **Image Optimization**: Compress and cache product images
- **Batch Operations**: Multi-select for bulk actions

## 📞 **Support & Contributing**

### Getting Help

- **Issues**: Create GitHub issues for bugs
- **Features**: Use GitHub discussions for feature requests
- **Documentation**: Check wiki for detailed guides

### Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

### Development Setup

```bash
# Install dependencies
npm install

# Start development server
npx expo start

# Run tests (when implemented)
npm test

# Lint code
npm run lint

# Type check
npx tsc --noEmit
```

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 **Acknowledgments**

- **Open Food Facts** - Product database API
- **Expo Team** - Amazing development platform
- **React Native Paper** - Beautiful Material Design components
- **Zustand** - Simple state management
- **Community** - Open source contributors and testers

---

**Built with ❤️ for smart pantry management**

_Ready for MVP testing and user feedback collection_
