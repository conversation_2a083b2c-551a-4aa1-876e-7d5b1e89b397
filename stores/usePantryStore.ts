import { create } from 'zustand';
import { Product, CreateProductInput, UpdateProductInput } from '@/types';
import { ProductCategory } from '@/enums';

interface PantryState {
  products: Product[];
  isLoading: boolean;
  searchQuery: string;
  selectedCategory: ProductCategory | null;
  sortBy: 'name' | 'date' | 'quantity';
}

interface PantryActions {
  addProduct: (product: CreateProductInput) => void;
  updateProduct: (product: UpdateProductInput) => void;
  removeProduct: (id: string) => void;
  updateQuantity: (id: string, quantity: number) => void;
  setProducts: (products: Product[]) => void;
  setLoading: (loading: boolean) => void;
  setSearchQuery: (query: string) => void;
  setSelectedCategory: (category: ProductCategory | null) => void;
  setSortBy: (sortBy: 'name' | 'date' | 'quantity') => void;
  getFilteredProducts: () => Product[];
  findProductByBarcode: (barcode: string) => Product | undefined;
  clearAllProducts: () => void;
}

type PantryStore = PantryState & PantryActions;

export const usePantryStore = create<PantryStore>((set, get) => ({
  // State
  products: [],
  isLoading: false,
  searchQuery: '',
  selectedCategory: null,
  sortBy: 'date',

  // Actions
  addProduct: (productInput: CreateProductInput) => {
    const newProduct: Product = {
      id: Date.now().toString(),
      ...productInput,
      quantity: productInput.quantity || 1,
      addedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isManuallyAdded: !productInput.barcode,
    };

    set((state) => ({
      products: [...state.products, newProduct],
    }));
  },

  updateProduct: (productUpdate: UpdateProductInput) => {
    set((state) => ({
      products: state.products.map((product) =>
        product.id === productUpdate.id
          ? {
              ...product,
              ...productUpdate,
              updatedAt: new Date().toISOString(),
            }
          : product
      ),
    }));
  },

  removeProduct: (id: string) => {
    set((state) => ({
      products: state.products.filter((product) => product.id !== id),
    }));
  },

  updateQuantity: (id: string, quantity: number) => {
    if (quantity <= 0) {
      get().removeProduct(id);
      return;
    }

    set((state) => ({
      products: state.products.map((product) =>
        product.id === id
          ? {
              ...product,
              quantity,
              updatedAt: new Date().toISOString(),
            }
          : product
      ),
    }));
  },

  setProducts: (products: Product[]) => {
    set({ products });
  },

  setLoading: (isLoading: boolean) => {
    set({ isLoading });
  },

  setSearchQuery: (searchQuery: string) => {
    set({ searchQuery });
  },

  setSelectedCategory: (selectedCategory: ProductCategory | null) => {
    set({ selectedCategory });
  },

  setSortBy: (sortBy: 'name' | 'date' | 'quantity') => {
    set({ sortBy });
  },

  getFilteredProducts: () => {
    const { products, searchQuery, selectedCategory, sortBy } = get();
    
    let filtered = products;

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter((product) =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.brand?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by category
    if (selectedCategory) {
      filtered = filtered.filter((product) => product.category === selectedCategory);
    }

    // Sort products
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'quantity':
          return b.quantity - a.quantity;
        case 'date':
        default:
          return new Date(b.addedAt).getTime() - new Date(a.addedAt).getTime();
      }
    });

    return filtered;
  },

  findProductByBarcode: (barcode: string) => {
    return get().products.find((product) => product.barcode === barcode);
  },

  clearAllProducts: () => {
    set({ products: [] });
  },
}));
