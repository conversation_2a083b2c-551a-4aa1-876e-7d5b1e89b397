import { create } from 'zustand';

interface SettingsState {
  language: string;
  theme: 'light' | 'dark' | 'auto';
  notifications: boolean;
}

interface SettingsActions {
  setLanguage: (language: string) => void;
  setTheme: (theme: 'light' | 'dark' | 'auto') => void;
  setNotifications: (notifications: boolean) => void;
  reset: () => void;
}

type SettingsStore = SettingsState & SettingsActions;

const initialState: SettingsState = {
  language: 'en',
  theme: 'auto',
  notifications: true,
};

export const useSettingsStore = create<SettingsStore>((set) => ({
  // State
  ...initialState,

  // Actions
  setLanguage: (language: string) => {
    set({ language });
  },

  setTheme: (theme: 'light' | 'dark' | 'auto') => {
    set({ theme });
  },

  setNotifications: (notifications: boolean) => {
    set({ notifications });
  },

  reset: () => {
    set(initialState);
  },
}));
