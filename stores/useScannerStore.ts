import { create } from 'zustand';
import { ScannerMode } from '@/enums';
import { ScannerState } from '@/types';

interface ScannerActions {
  setMode: (mode: ScannerMode) => void;
  setScanning: (isScanning: boolean) => void;
  setPermission: (hasPermission: boolean | null) => void;
  toggleMode: () => void;
  reset: () => void;
}

type ScannerStore = ScannerState & ScannerActions;

export const useScannerStore = create<ScannerStore>((set, get) => ({
  // State
  mode: ScannerMode.ADD,
  isScanning: false,
  hasPermission: null,

  // Actions
  setMode: (mode: ScannerMode) => {
    set({ mode });
  },

  setScanning: (isScanning: boolean) => {
    set({ isScanning });
  },

  setPermission: (hasPermission: boolean | null) => {
    set({ hasPermission });
  },

  toggleMode: () => {
    const { mode } = get();
    set({
      mode: mode === ScannerMode.ADD ? ScannerMode.REMOVE : ScannerMode.ADD,
    });
  },

  reset: () => {
    set({
      mode: ScannerMode.ADD,
      isScanning: false,
    });
  },
}));
