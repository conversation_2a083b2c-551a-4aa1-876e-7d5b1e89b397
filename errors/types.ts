export type ErrorClassFields = {
  type: ErrorTypes;
  code: number;
  message?: string;
};

export enum ErrorTypes {
  UNKNOWN = "UNKNOWN",
  BARCODE_SCANNER = "BARCODE_SCANNER",
  CAMERA_PERMISSION = "CAMERA_PERMISSION",
  PRODUCT_API = "PRODUCT_API",
  PRODUCT_NOT_FOUND = "PRODUCT_NOT_FOUND",
  STORAGE_ERROR = "STORAGE_ERROR",
  NETWORK_ERROR = "NETWORK_ERROR",
  VALIDATION_ERROR = "VALIDATION_ERROR",
  PANTRY_OPERATION = "PANTRY_OPERATION",
}
