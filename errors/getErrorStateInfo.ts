export const getErrorStatus = (error: unknown): number => {
  if (error && typeof error === 'object' && 'status' in error) {
    return (error as any).status;
  }
  if (error && typeof error === 'object' && 'response' in error) {
    return (error as any).response?.status || 500;
  }
  return 500;
};

export const getErrorMessage = (error: unknown): string => {
  if (error && typeof error === 'object' && 'message' in error) {
    return (error as any).message;
  }
  if (error && typeof error === 'string') {
    return error;
  }
  return 'An unknown error occurred';
};
