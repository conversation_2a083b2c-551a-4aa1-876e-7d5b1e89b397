import { ErrorBase } from "./ErrorBase";
import { ErrorTypes } from "./types";

export class <PERSON>rrorBarcodeScanner extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.BARCODE_SCANNER, error);
  }
}

export class <PERSON>rrorCameraPermission extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.CAMERA_PERMISSION, error);
  }
}

export class ErrorProductApi extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.PRODUCT_API, error);
  }
}

export class ErrorProductNotFound extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.PRODUCT_NOT_FOUND, error);
  }
}

export class ErrorStorageError extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.STORAGE_ERROR, error);
  }
}

export class ErrorNetworkError extends <PERSON><PERSON>rBase {
  constructor(error?: unknown) {
    super(ErrorTypes.NETWORK_ERROR, error);
  }
}

export class ErrorValidationError extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.VALIDATION_ERROR, error);
  }
}

export class ErrorPantryOperation extends ErrorBase {
  constructor(error?: unknown) {
    super(ErrorTypes.PANTRY_OPERATION, error);
  }
}
