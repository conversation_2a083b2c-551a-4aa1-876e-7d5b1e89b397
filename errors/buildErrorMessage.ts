import { ErrorClassFields, ErrorTypes } from "./types";

export interface ToastProps {
  title: string;
  message: string;
  type: 'error' | 'warning' | 'info' | 'success';
}

export const buildErrorMessage = (error: ErrorClassFields): ToastProps => {
  const getErrorTitle = (type: ErrorTypes): string => {
    switch (type) {
      case ErrorTypes.BARCODE_SCANNER:
        return 'Scanner Error';
      case ErrorTypes.CAMERA_PERMISSION:
        return 'Camera Permission';
      case ErrorTypes.PRODUCT_API:
        return 'Product API Error';
      case ErrorTypes.PRODUCT_NOT_FOUND:
        return 'Product Not Found';
      case ErrorTypes.STORAGE_ERROR:
        return 'Storage Error';
      case ErrorTypes.NETWORK_ERROR:
        return 'Network Error';
      case ErrorTypes.VALIDATION_ERROR:
        return 'Validation Error';
      case ErrorTypes.PANTRY_OPERATION:
        return 'Pantry Operation Error';
      default:
        return 'Unknown Error';
    }
  };

  const getErrorMessage = (type: ErrorTypes, message?: string): string => {
    if (message) return message;
    
    switch (type) {
      case ErrorTypes.BARCODE_SCANNER:
        return 'Failed to scan barcode. Please try again.';
      case ErrorTypes.CAMERA_PERMISSION:
        return 'Camera permission is required to scan barcodes.';
      case ErrorTypes.PRODUCT_API:
        return 'Failed to fetch product information.';
      case ErrorTypes.PRODUCT_NOT_FOUND:
        return 'Product not found in database.';
      case ErrorTypes.STORAGE_ERROR:
        return 'Failed to save data locally.';
      case ErrorTypes.NETWORK_ERROR:
        return 'Network connection error.';
      case ErrorTypes.VALIDATION_ERROR:
        return 'Invalid data provided.';
      case ErrorTypes.PANTRY_OPERATION:
        return 'Failed to update pantry.';
      default:
        return 'An unexpected error occurred.';
    }
  };

  return {
    title: getErrorTitle(error.type),
    message: getErrorMessage(error.type, error.message),
    type: 'error',
  };
};
