{"common": {"add": "Add", "remove": "Remove", "edit": "Edit", "delete": "Delete", "save": "Save", "cancel": "Cancel", "confirm": "Confirm", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info", "search": "Search", "quantity": "Quantity", "name": "Name", "brand": "Brand", "category": "Category", "description": "Description", "expiryDate": "Expiry Date", "unit": "Unit"}, "navigation": {"pantry": "Pantry", "scanner": "Scanner", "settings": "Settings"}, "pantry": {"title": "My Pantry", "empty": "Your pantry is empty", "emptyDescription": "Start by scanning or adding products to your pantry", "addProduct": "Add Product", "searchPlaceholder": "Search products...", "filterByCategory": "Filter by category", "sortBy": "Sort by", "sortByName": "Name", "sortByDate": "Date Added", "sortByQuantity": "Quantity", "productAdded": "Product added to pantry", "productUpdated": "Product updated", "productRemoved": "Product removed from pantry", "quantityUpdated": "Quantity updated"}, "scanner": {"title": "Barcode Scanner", "addMode": "Add Mode", "removeMode": "Remove Mode", "switchToAdd": "Switch to Add Mode", "switchToRemove": "Switch to Remove Mode", "scanPrompt": "Point camera at barcode", "cameraPermissionRequired": "Camera permission is required", "requestPermission": "Request Permission", "scanAgain": "<PERSON><PERSON>", "productFound": "Product found!", "productNotFound": "Product not found", "addManually": "Add Manually", "scanningError": "Error scanning barcode"}, "product": {"details": "Product Details", "addNew": "Add New Product", "editProduct": "Edit Product", "manualAdd": "Manual Add", "barcodeLabel": "Barcode", "nameRequired": "Product name is required", "quantityRequired": "Quantity is required", "categoryRequired": "Category is required", "invalidQuantity": "Please enter a valid quantity", "confirmDelete": "Are you sure you want to delete this product?", "deleteConfirmation": "This action cannot be undone."}, "categories": {"FOOD": "Food", "BEVERAGES": "Beverages", "HOUSEHOLD": "Household", "PERSONAL_CARE": "Personal Care", "OTHER": "Other"}, "settings": {"title": "Settings", "language": "Language", "theme": "Theme", "notifications": "Notifications", "about": "About", "version": "Version", "clearData": "Clear All Data", "clearDataConfirmation": "Are you sure you want to clear all pantry data?", "dataCleared": "All data has been cleared"}, "errors": {"networkError": "Network connection error", "storageError": "Failed to save data", "cameraError": "Camera access error", "scannerError": "Scanner error", "productApiError": "Failed to fetch product information", "validationError": "Invalid data provided", "unknownError": "An unexpected error occurred"}}