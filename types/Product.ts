import { ProductCategory } from "@/enums";

export interface Product {
  id: string;
  barcode?: string;
  name: string;
  brand?: string;
  category: ProductCategory;
  imageUrl?: string;
  description?: string;
  quantity: number;
  addedAt: string;
  updatedAt: string;
  expiryDate?: string;
  isManuallyAdded: boolean;
}

export interface ProductApiResponse {
  product: {
    _id: string;
    product_name: string;
    brands?: string;
    categories?: string;
    image_url?: string;
    generic_name?: string;
    quantity?: string;
  };
  status: number;
  status_verbose: string;
}

export interface CreateProductInput {
  barcode?: string;
  name: string;
  brand?: string;
  category: ProductCategory;
  imageUrl?: string;
  description?: string;
  quantity?: number;
  expiryDate?: string;
}

export interface UpdateProductInput {
  id: string;
  name?: string;
  brand?: string;
  category?: ProductCategory;
  imageUrl?: string;
  description?: string;
  quantity?: number;
  expiryDate?: string;
}
