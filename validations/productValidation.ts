import { ProductCategory } from "@/enums";
import { z } from "zod";

export const createProductSchema = z.object({
  barcode: z.string().optional(),
  name: z.string().min(1, "Product name is required").max(100, "Name too long"),
  brand: z.string().max(50, "Brand name too long").optional(),
  category: z.nativeEnum(ProductCategory, {
    errorMap: () => ({ message: "Please select a valid category" }),
  }),
  imageUrl: z.string().url("Invalid image URL").optional().or(z.literal("")),
  description: z.string().max(500, "Description too long").optional(),
  quantity: z
    .number()
    .min(1, "Quantity must be at least 1")
    .max(9999, "Quantity too large"),
  expiryDate: z.string().optional(),
});

export const updateProductSchema = z.object({
  id: z.string().min(1, "Product ID is required"),
  name: z
    .string()
    .min(1, "Product name is required")
    .max(100, "Name too long")
    .optional(),
  brand: z.string().max(50, "Brand name too long").optional(),
  category: z.nativeEnum(ProductCategory).optional(),
  imageUrl: z.string().url("Invalid image URL").optional().or(z.literal("")),
  description: z.string().max(500, "Description too long").optional(),
  quantity: z
    .number()
    .min(1, "Quantity must be at least 1")
    .max(9999, "Quantity too large")
    .optional(),
  expiryDate: z.string().optional(),
});

export const quantityUpdateSchema = z.object({
  id: z.string().min(1, "Product ID is required"),
  quantity: z
    .number()
    .min(0, "Quantity cannot be negative")
    .max(9999, "Quantity too large"),
});

export type CreateProductFormData = z.infer<typeof createProductSchema>;
export type UpdateProductFormData = z.infer<typeof updateProductSchema>;
export type QuantityUpdateFormData = z.infer<typeof quantityUpdateSchema>;
