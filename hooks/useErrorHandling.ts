import { useToast } from './useToast';
import { buildErrorMessage, ErrorClassFields, ToastProps } from '@/errors';

export const useErrorHandling = () => {
  const { showToast } = useToast();

  const withErrorHandling = async <T,>(
    operation: () => Promise<T>,
    extraOnError?: () => void
  ): Promise<T | undefined> => {
    try {
      return await operation();
    } catch (error) {
      const errorMessage: ToastProps = buildErrorMessage(
        error as ErrorClassFields
      );
      showToast(errorMessage);

      if (extraOnError) {
        extraOnError();
      }
    }
  };

  return {
    withErrorHandling,
  };
};
