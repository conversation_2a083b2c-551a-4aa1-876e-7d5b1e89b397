import { useEffect } from 'react';
import { usePantryStore, useSettingsStore } from '@/stores';
import { StorageService } from '@/utils';
import { useErrorHandling } from './useErrorHandling';

export const usePersistentStorage = () => {
  const { setProducts, products } = usePantryStore();
  const { setLanguage, setTheme, setNotifications, language, theme, notifications } = useSettingsStore();
  const { withErrorHandling } = useErrorHandling();

  // Load data on app start
  useEffect(() => {
    const loadData = async () => {
      await withErrorHandling(async () => {
        // Load products
        const savedProducts = await StorageService.loadProducts();
        setProducts(savedProducts);

        // Load settings
        const savedSettings = await StorageService.loadSettings();
        if (savedSettings) {
          setLanguage(savedSettings.language || 'en');
          setTheme(savedSettings.theme || 'auto');
          setNotifications(savedSettings.notifications ?? true);
        }
      });
    };

    loadData();
  }, []);

  // Save products when they change
  useEffect(() => {
    if (products.length > 0) {
      withErrorHandling(async () => {
        await StorageService.saveProducts(products);
      });
    }
  }, [products]);

  // Save settings when they change
  useEffect(() => {
    const settings = { language, theme, notifications };
    withErrorHandling(async () => {
      await StorageService.saveSettings(settings);
    });
  }, [language, theme, notifications]);

  const clearAllData = async () => {
    await withErrorHandling(async () => {
      await StorageService.clearAllData();
      setProducts([]);
    });
  };

  return {
    clearAllData,
  };
};
