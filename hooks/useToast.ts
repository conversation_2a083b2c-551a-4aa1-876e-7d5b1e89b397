import { useState, useCallback } from 'react';
import { ToastProps } from '@/errors';

interface ToastState {
  visible: boolean;
  props: ToastProps | null;
}

export const useToast = () => {
  const [toast, setToast] = useState<ToastState>({
    visible: false,
    props: null,
  });

  const showToast = useCallback((props: ToastProps) => {
    setToast({
      visible: true,
      props,
    });

    // Auto hide after 4 seconds
    setTimeout(() => {
      hideToast();
    }, 4000);
  }, []);

  const hideToast = useCallback(() => {
    setToast({
      visible: false,
      props: null,
    });
  }, []);

  return {
    toast,
    showToast,
    hideToast,
  };
};
