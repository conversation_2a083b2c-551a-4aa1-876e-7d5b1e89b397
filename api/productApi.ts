import { axiosInstance } from '@/middleware/axiosInstance';
import { ProductApiResponse } from '@/types';
import { ErrorProductApi, ErrorProductNotFound } from '@/errors';

export class ProductApi {
  static async getProductByBarcode(barcode: string): Promise<ProductApiResponse> {
    try {
      const response = await axiosInstance.get(`/product/${barcode}.json`);
      
      if (response.data.status === 0) {
        throw new ErrorProductNotFound(`Product with barcode ${barcode} not found`);
      }

      return response.data;
    } catch (error) {
      if (error instanceof ErrorProductNotFound) {
        throw error;
      }
      throw new ErrorProductApi(error);
    }
  }

  static async searchProducts(query: string, page: number = 1): Promise<any> {
    try {
      const response = await axiosInstance.get('/cgi/search.pl', {
        params: {
          search_terms: query,
          search_simple: 1,
          action: 'process',
          json: 1,
          page,
          page_size: 20,
        },
      });

      return response.data;
    } catch (error) {
      throw new Error<PERSON>roduct<PERSON>pi(error);
    }
  }
}
