import { MD3LightTheme, MD3DarkTheme } from 'react-native-paper';

export const lightTheme = {
  ...MD3LightTheme,
  colors: {
    ...MD3LightTheme.colors,
    primary: '#FEA405',
    secondary: '#B66B00',
    tertiary: '#FFD477',
    surface: '#F7F7F7',
    onSurface: '#2B2B2B',
    background: '#F7F7F7',
    onBackground: '#2B2B2B',
  },
};

export const darkTheme = {
  ...MD3DarkTheme,
  colors: {
    ...MD3DarkTheme.colors,
    primary: '#FEA405',
    secondary: '#B66B00',
    tertiary: '#FFD477',
    surface: '#1C1C1C',
    onSurface: '#F7F7F7',
    background: '#121212',
    onBackground: '#F7F7F7',
  },
};

export const spacing = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const borderRadius = {
  sm: 4,
  md: 8,
  lg: 12,
  xl: 16,
};

export const fontSize = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 32,
};
