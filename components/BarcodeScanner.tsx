import { spacing } from "@/constants/theme";
import { ScannerMode } from "@/enums";
import { ErrorCameraPermission } from "@/errors";
import { useErrorHandling } from "@/hooks";
import { useScannerStore } from "@/stores";
import { ScanResult } from "@/types";
import { useFocusEffect, useIsFocused } from "@react-navigation/native";
import { Camera, CameraView } from "expo-camera";
import React, { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Dimensions, StyleSheet, TouchableOpacity, View } from "react-native";
import { Button, IconButton, Surface, Text } from "react-native-paper";

interface BarcodeScannerProps {
  onScan: (result: ScanResult) => void;
}

const { width } = Dimensions.get("window");

export const BarcodeScanner: React.FC<BarcodeScannerProps> = ({ onScan }) => {
  const { t } = useTranslation();
  const { mode, hasPermission, setPermission, setScanning, toggleMode } =
    useScannerStore();
  const [scanned, setScanned] = useState(false);
  const cameraRef = React.useRef<CameraView>(null);
  const { withErrorHandling } = useErrorHandling();
  const isFocused = useIsFocused();

  useEffect(() => {
    getCameraPermissions();
  }, []);

  useFocusEffect(
    useCallback(() => {
      setScanned(false);
      setScanning(true);
      getCameraPermissions();
      return () => {
        setScanning(false);
      };
    }, [setScanning])
  );

  const getCameraPermissions = async () =>
    withErrorHandling(async () => {
      const { status } = await Camera.requestCameraPermissionsAsync();
      setPermission(status === "granted");
      if (status !== "granted") {
        throw new ErrorCameraPermission("Camera permission denied");
      }
    });

  const handleBarCodeScanned = ({
    type,
    data,
  }: {
    type: string;
    data: string;
  }) =>
    withErrorHandling(async () => {
      if (scanned) return;

      setScanned(true);
      setScanning(false);

      onScan({ type, data });

      setTimeout(() => {
        setScanned(false);
        setScanning(true);
      }, 2000);
    });

  if (hasPermission === null) {
    return (
      <View style={styles.container}>
        <Text variant="bodyLarge">{t("scanner.cameraPermissionRequired")}</Text>
      </View>
    );
  }

  if (hasPermission === false) {
    return (
      <View style={styles.container}>
        <Text variant="bodyLarge" style={styles.permissionText}>
          {t("scanner.cameraPermissionRequired")}
        </Text>
        <Button
          mode="contained"
          onPress={getCameraPermissions}
          style={styles.button}
        >
          {t("scanner.requestPermission")}
        </Button>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {isFocused && (
        <CameraView
          key={isFocused ? "camera-active" : "camera-inactive"}
          ref={cameraRef}
          style={styles.camera}
          facing="back"
          onBarcodeScanned={scanned ? undefined : handleBarCodeScanned}
          barcodeScannerSettings={{
            barcodeTypes: [
              "ean13",
              "ean8",
              "upc_a",
              "upc_e",
              "code128",
              "code39",
            ],
          }}
        >
          <View style={styles.overlay}>
            <TouchableOpacity onPress={toggleMode}>
              <Surface style={styles.modeToggle}>
                <Text variant="titleMedium" style={styles.modeText}>
                  {mode === ScannerMode.ADD
                    ? t("scanner.addMode")
                    : t("scanner.removeMode")}
                </Text>
                <IconButton
                  icon={mode === ScannerMode.ADD ? "plus" : "minus"}
                  size={24}
                  iconColor={mode === ScannerMode.ADD ? "#4CAF50" : "#F44336"}
                />
              </Surface>
            </TouchableOpacity>

            <View style={styles.scanFrame}>
              <View style={[styles.corner, styles.topLeft]} />
              <View style={[styles.corner, styles.topRight]} />
              <View style={[styles.corner, styles.bottomLeft]} />
              <View style={[styles.corner, styles.bottomRight]} />
            </View>

            <Surface style={styles.instructions}>
              <Text variant="bodyMedium" style={styles.instructionText}>
                {scanned ? t("scanner.scanAgain") : t("scanner.scanPrompt")}
              </Text>
            </Surface>
          </View>
        </CameraView>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: "center",
  },
  camera: {
    flex: 1,
  },
  overlay: {
    flex: 1,
    backgroundColor: "transparent",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: spacing.xl,
    paddingTop: spacing.xxl,
  },
  modeToggle: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.sm,
    borderRadius: 25,
  },
  modeText: {
    marginRight: spacing.sm,
    fontWeight: "bold",
  },
  scanFrame: {
    width: width * 0.7,
    height: width * 0.7,
    position: "relative",
  },
  corner: {
    position: "absolute",
    width: 30,
    height: 30,
    borderColor: "#FEA405",
    borderWidth: 3,
  },
  topLeft: {
    top: 0,
    left: 0,
    borderRightWidth: 0,
    borderBottomWidth: 0,
  },
  topRight: {
    top: 0,
    right: 0,
    borderLeftWidth: 0,
    borderBottomWidth: 0,
  },
  bottomLeft: {
    bottom: 0,
    left: 0,
    borderRightWidth: 0,
    borderTopWidth: 0,
  },
  bottomRight: {
    bottom: 0,
    right: 0,
    borderLeftWidth: 0,
    borderTopWidth: 0,
  },
  instructions: {
    paddingHorizontal: spacing.lg,
    paddingVertical: spacing.md,
    borderRadius: 20,
    backgroundColor: "rgba(0, 0, 0, 0.7)",
  },
  instructionText: {
    color: "white",
    textAlign: "center",
  },
  permissionText: {
    textAlign: "center",
    marginBottom: spacing.lg,
  },
  button: {
    marginTop: spacing.md,
  },
});
