import { borderRadius, spacing } from "@/constants/theme";
import { Product } from "@/types";
import { formatProductQuantity } from "@/utils";
import React from "react";
import { useTranslation } from "react-i18next";
import { Image, StyleSheet, View } from "react-native";
import { Card, Chip, IconButton, Text } from "react-native-paper";

interface ProductCardProps {
  product: Product;
  onEdit?: (product: Product) => void;
  onDelete?: (product: Product) => void;
  onQuantityChange?: (product: Product, newQuantity: number) => void;
}

export const ProductCard: React.FC<ProductCardProps> = ({
  product,
  onEdit,
  onDelete,
  onQuantityChange,
}) => {
  const { t } = useTranslation();

  const handleQuantityIncrease = () => {
    onQuantityChange?.(product, product.quantity + 1);
  };

  const handleQuantityDecrease = () => {
    if (product.quantity > 1) {
      onQuantityChange?.(product, product.quantity - 1);
    } else {
      onDelete?.(product);
    }
  };

  return (
    <Card style={styles.card}>
      <View style={styles.content}>
        {product.imageUrl && (
          <Image source={{ uri: product.imageUrl }} style={styles.image} />
        )}

        <View style={styles.info}>
          <View style={styles.header}>
            <Text variant="titleMedium" style={styles.name} numberOfLines={2}>
              {product.name}
            </Text>
            {onEdit && (
              <IconButton
                icon="pencil"
                size={20}
                onPress={() => onEdit(product)}
              />
            )}
          </View>

          {product.brand && (
            <Text variant="bodySmall" style={styles.brand}>
              {product.brand}
            </Text>
          )}

          <View style={styles.details}>
            <Chip mode="outlined">{t(`categories.${product.category}`)}</Chip>

            <View style={styles.quantityContainer}>
              <IconButton
                icon="minus"
                size={20}
                onPress={handleQuantityDecrease}
                style={styles.quantityButton}
              />
              <Text variant="titleMedium" style={styles.quantity}>
                {formatProductQuantity(product.quantity)}
              </Text>
              <IconButton
                icon="plus"
                size={20}
                onPress={handleQuantityIncrease}
                style={styles.quantityButton}
              />
            </View>
          </View>

          {product.expiryDate && (
            <Text variant="bodySmall" style={styles.expiry}>
              Expires: {new Date(product.expiryDate).toLocaleDateString()}
            </Text>
          )}
        </View>
      </View>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    marginHorizontal: spacing.md,
    marginVertical: spacing.sm,
    borderRadius: borderRadius.lg,
  },
  content: {
    flexDirection: "row",
    padding: spacing.md,
  },
  image: {
    width: 60,
    height: 60,
    borderRadius: borderRadius.md,
    marginRight: spacing.md,
  },
  info: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
  },
  name: {
    flex: 1,
    fontWeight: "bold",
    marginRight: spacing.sm,
  },
  brand: {
    color: "#666",
    marginBottom: spacing.sm,
  },
  details: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  categoryChip: {
    height: 28,
  },
  quantityContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  quantityButton: {
    margin: 0,
  },
  quantity: {
    marginHorizontal: spacing.sm,
    minWidth: 40,
    textAlign: "center",
  },
  expiry: {
    color: "#FF6B6B",
    fontStyle: "italic",
  },
});
