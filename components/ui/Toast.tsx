import React from 'react';
import { StyleSheet, View } from 'react-native';
import { Snackbar, Text } from 'react-native-paper';
import { ToastProps } from '@/errors';

interface ToastComponentProps extends ToastProps {
  visible: boolean;
  onDismiss: () => void;
}

export const Toast: React.FC<ToastComponentProps> = ({
  visible,
  title,
  message,
  type,
  onDismiss,
}) => {
  const getBackgroundColor = () => {
    switch (type) {
      case 'error':
        return '#F44336';
      case 'warning':
        return '#FF9800';
      case 'success':
        return '#4CAF50';
      case 'info':
      default:
        return '#2196F3';
    }
  };

  return (
    <Snackbar
      visible={visible}
      onDismiss={onDismiss}
      duration={4000}
      style={[styles.snackbar, { backgroundColor: getBackgroundColor() }]}
      action={{
        label: 'Dismiss',
        onPress: onDismiss,
        textColor: '#FFFFFF',
      }}
    >
      <View style={styles.content}>
        <Text style={styles.title} variant="titleSmall">
          {title}
        </Text>
        <Text style={styles.message} variant="bodySmall">
          {message}
        </Text>
      </View>
    </Snackbar>
  );
};

const styles = StyleSheet.create({
  snackbar: {
    marginBottom: 100,
  },
  content: {
    flex: 1,
  },
  title: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    marginBottom: 4,
  },
  message: {
    color: '#FFFFFF',
  },
});
