import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useColorScheme } from "react-native";
import { PaperProvider } from "react-native-paper";
import "react-native-reanimated";
import { SafeAreaProvider } from "react-native-safe-area-context";

import { Toast } from "@/components/ui";
import { darkTheme, lightTheme } from "@/constants/theme";
import { usePersistentStorage, useToast } from "@/hooks";
import "@/locales/i18n";

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });

  // Initialize persistent storage
  usePersistentStorage();

  // Toast system
  const { toast, hideToast } = useToast();

  if (!loaded) {
    return null;
  }

  const theme = colorScheme === "dark" ? darkTheme : lightTheme;

  return (
    <SafeAreaProvider>
      <PaperProvider theme={darkTheme}>
        <Stack>
          <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
          <Stack.Screen
            name="product/[id]"
            options={{ title: "Product Details" }}
          />
          <Stack.Screen
            name="add-product"
            options={{ title: "Add Product", headerShown: false }}
          />
          <Stack.Screen name="+not-found" />
        </Stack>
        <StatusBar style={colorScheme === "dark" ? "light" : "dark"} />

        {toast.props && (
          <Toast
            visible={toast.visible}
            onDismiss={hideToast}
            {...toast.props}
          />
        )}
      </PaperProvider>
    </SafeAreaProvider>
  );
}
