import { router } from "expo-router";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { FlatList, Platform, StyleSheet, View } from "react-native";
import { Appbar, Chip, FAB, Menu, Searchbar } from "react-native-paper";

import { EmptyState, LoadingSpinner, ProductCard } from "@/components/ui";
import { spacing } from "@/constants/theme";
import { ProductCategory } from "@/enums";
import { useErrorHandling } from "@/hooks";
import { usePantryStore } from "@/stores";
import { Product } from "@/types";

export default function PantryScreen() {
  const { t } = useTranslation();
  const { withErrorHandling } = useErrorHandling();

  const {
    isLoading,
    searchQuery,
    selectedCategory,
    sortBy,
    setSearchQuery,
    setSelectedCategory,
    setSortBy,
    getFilteredProducts,
    updateQuantity,
    removeProduct,
  } = usePantryStore();

  const [menuVisible, setMenuVisible] = useState(false);

  const filteredProducts = getFilteredProducts();

  const handleProductEdit = (product: Product) => {
    router.push(`/product/${product.id}`);
  };

  const handleProductDelete = async (product: Product) => {
    await withErrorHandling(async () => {
      removeProduct(product.id);
    });
  };

  const handleQuantityChange = async (
    product: Product,
    newQuantity: number
  ) => {
    await withErrorHandling(async () => {
      updateQuantity(product.id, newQuantity);
    });
  };

  const handleAddProduct = () => {
    router.push("/add-product");
  };

  const renderProduct = ({ item }: { item: Product }) => (
    <ProductCard
      product={item}
      onEdit={handleProductEdit}
      onDelete={handleProductDelete}
      onQuantityChange={handleQuantityChange}
    />
  );

  if (isLoading) {
    return <LoadingSpinner message={t("common.loading")} />;
  }

  return (
    <View style={styles.container}>
      <Appbar.Header style={styles.header}>
        <Appbar.Content title={t("pantry.title")} />
        <Menu
          visible={menuVisible}
          onDismiss={() => setMenuVisible(false)}
          anchor={
            <Appbar.Action icon="sort" onPress={() => setMenuVisible(true)} />
          }
        >
          <Menu.Item
            onPress={() => {
              setSortBy("name");
              setMenuVisible(false);
            }}
            title={t("pantry.sortByName")}
            leadingIcon={sortBy === "name" ? "check" : undefined}
          />
          <Menu.Item
            onPress={() => {
              setSortBy("date");
              setMenuVisible(false);
            }}
            title={t("pantry.sortByDate")}
            leadingIcon={sortBy === "date" ? "check" : undefined}
          />
          <Menu.Item
            onPress={() => {
              setSortBy("quantity");
              setMenuVisible(false);
            }}
            title={t("pantry.sortByQuantity")}
            leadingIcon={sortBy === "quantity" ? "check" : undefined}
          />
        </Menu>
      </Appbar.Header>

      <View style={styles.searchContainer}>
        <Searchbar
          placeholder={t("pantry.searchPlaceholder")}
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />
      </View>

      <View style={styles.filtersContainer}>
        <FlatList
          horizontal
          showsHorizontalScrollIndicator={false}
          data={[null, ...Object.values(ProductCategory)]}
          keyExtractor={(item) => item || "all"}
          renderItem={({ item }) => (
            <Chip
              mode={selectedCategory === item ? "flat" : "outlined"}
              onPress={() => setSelectedCategory(item)}
              style={styles.categoryChip}
            >
              {item ? t(`categories.${item}`) : "All"}
            </Chip>
          )}
        />
      </View>

      {filteredProducts.length === 0 ? (
        <EmptyState
          icon="basket-outline"
          title={t("pantry.empty")}
          description={t("pantry.emptyDescription")}
          actionLabel={t("pantry.addProduct")}
          onAction={handleAddProduct}
        />
      ) : (
        <FlatList
          data={filteredProducts}
          renderItem={renderProduct}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      )}

      <FAB icon="plus" style={styles.fab} onPress={handleAddProduct} />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F7F7F7",
  },
  header: {
    paddingHorizontal: spacing.sm,
    elevation: 2,
  },
  searchContainer: {
    paddingHorizontal: spacing.md,
    paddingVertical: spacing.sm,
  },
  searchbar: {
    elevation: 2,
  },
  filtersContainer: {
    paddingHorizontal: spacing.md,
    paddingBottom: spacing.sm,
  },
  categoryChip: {
    marginRight: spacing.sm,
  },
  listContainer: {
    paddingBottom: Platform.OS === "ios" ? 120 : 100,
  },
  fab: {
    position: "absolute",
    margin: spacing.md,
    right: 0,
    bottom: 0,
    backgroundColor: "#FEA405",
  },
});
