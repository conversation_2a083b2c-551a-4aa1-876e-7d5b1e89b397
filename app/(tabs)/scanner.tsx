import { router } from "expo-router";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { StyleSheet, View } from "react-native";
import { Button, Dialog, Portal, Text } from "react-native-paper";

import { ProductApi } from "@/api";
import { BarcodeScanner } from "@/components/BarcodeScanner";
import { LoadingSpinner } from "@/components/ui";
import { spacing } from "@/constants/theme";
import { ScannerMode } from "@/enums";
import { ErrorProductNotFound } from "@/errors";
import { useErrorHandling } from "@/hooks";
import { usePantryStore, useScannerStore } from "@/stores";
import { ScanResult } from "@/types";
import { isValidBarcode, mapApiResponseToProduct } from "@/utils";

export default function ScannerScreen() {
  const { t } = useTranslation();
  const { withErrorHandling } = useErrorHandling();

  const { mode } = useScannerStore();
  const { addProduct, updateQuantity, findProductByBarcode } = usePantryStore();

  const [isProcessing, setIsProcessing] = useState(false);
  const [dialogVisible, setDialogVisible] = useState(false);
  const [dialogData, setDialogData] = useState<{
    type: "success" | "not_found" | "error";
    title: string;
    message: string;
    barcode?: string;
  } | null>(null);

  const handleScanResult = async (result: ScanResult) => {
    if (!isValidBarcode(result.data)) {
      showDialog("error", t("scanner.scanningError"), "Invalid barcode format");
      return;
    }

    setIsProcessing(true);

    await withErrorHandling(
      async () => {
        const existingProduct = findProductByBarcode(result.data);

        if (mode === ScannerMode.REMOVE) {
          if (existingProduct) {
            updateQuantity(
              existingProduct.id,
              Math.max(0, existingProduct.quantity - 1)
            );
            showDialog(
              "success",
              t("pantry.quantityUpdated"),
              `${existingProduct.name} quantity updated`
            );
          } else {
            showDialog(
              "not_found",
              t("scanner.productNotFound"),
              "Product not found in your pantry"
            );
          }
          return;
        }

        // ADD mode
        if (existingProduct) {
          updateQuantity(existingProduct.id, existingProduct.quantity + 1);
          showDialog(
            "success",
            t("pantry.quantityUpdated"),
            `${existingProduct.name} quantity updated`
          );
          return;
        }

        // Try to fetch product from API
        try {
          const apiResponse = await ProductApi.getProductByBarcode(result.data);
          const productData = mapApiResponseToProduct(apiResponse, result.data);

          addProduct(productData);
          showDialog(
            "success",
            t("scanner.productFound"),
            `${productData.name} added to pantry`
          );
        } catch (error) {
          if (error instanceof ErrorProductNotFound) {
            showDialog(
              "not_found",
              t("scanner.productNotFound"),
              "Would you like to add it manually?",
              result.data
            );
          } else {
            throw error;
          }
        }
      },
      () => {
        setIsProcessing(false);
      }
    );

    setIsProcessing(false);
  };

  const handleScanError = (error: Error) => {
    showDialog("error", t("scanner.scanningError"), error.message);
  };

  const showDialog = (
    type: "success" | "not_found" | "error",
    title: string,
    message: string,
    barcode?: string
  ) => {
    setDialogData({ type, title, message, barcode });
    setDialogVisible(true);
  };

  const hideDialog = () => {
    setDialogVisible(false);
    setDialogData(null);
  };

  const handleManualAdd = () => {
    hideDialog();
    router.push({
      pathname: "/add-product",
      params: { barcode: dialogData?.barcode },
    });
  };

  if (isProcessing) {
    return <LoadingSpinner message="Processing barcode..." />;
  }

  return (
    <View style={styles.container}>
      <BarcodeScanner onScan={handleScanResult} />

      <Portal>
        <Dialog visible={dialogVisible} onDismiss={hideDialog}>
          <Dialog.Title>{dialogData?.title}</Dialog.Title>
          <Dialog.Content>
            <Text variant="bodyMedium">{dialogData?.message}</Text>
          </Dialog.Content>
          <Dialog.Actions>
            {dialogData?.type === "not_found" && dialogData.barcode && (
              <Button onPress={handleManualAdd}>
                {t("scanner.addManually")}
              </Button>
            )}
            <Button onPress={hideDialog}>OK</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#000000",
  },
  header: {
    paddingHorizontal: spacing.sm,
    elevation: 2,
  },
});
