import { zodResolver } from "@hookform/resolvers/zod";
import { router, useLocalSearchParams } from "expo-router";
import React, { useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { <PERSON>ert, ScrollView, StyleSheet, View } from "react-native";
import {
  Appbar,
  Button,
  Card,
  Chip,
  Menu,
  Text,
  TextInput,
} from "react-native-paper";

import { spacing } from "@/constants/theme";
import { ProductCategory } from "@/enums";
import { useErrorHandling } from "@/hooks";
import { usePantryStore } from "@/stores";
import { CreateProductFormData, createProductSchema } from "@/validations";

export default function AddProductScreen() {
  const { t } = useTranslation();
  const { withErrorHandling } = useErrorHandling();
  const { barcode } = useLocalSearchParams<{ barcode?: string }>();

  const { addProduct } = usePantryStore();

  const [categoryMenuVisible, setCategoryMenuVisible] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const {
    control,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<CreateProductFormData>({
    resolver: zodResolver(createProductSchema),
    defaultValues: {
      barcode: barcode || "",
      name: "",
      brand: "",
      category: ProductCategory.OTHER,
      description: "",
      quantity: 1,
      imageUrl: "",
    },
  });

  const selectedCategory = watch("category");

  const onSubmit = async (data: CreateProductFormData) => {
    setIsSubmitting(true);

    await withErrorHandling(
      async () => {
        addProduct(data);

        Alert.alert(t("common.success"), t("pantry.productAdded"), [
          {
            text: "OK",
            onPress: () => router.back(),
          },
        ]);
      },
      () => {
        setIsSubmitting(false);
      }
    );

    setIsSubmitting(false);
  };

  const handleCategorySelect = (category: ProductCategory) => {
    setValue("category", category);
    setCategoryMenuVisible(false);
  };

  return (
    <View style={styles.container}>
      <Appbar.Header style={styles.header}>
        <Appbar.BackAction onPress={() => router.back()} />
        <Appbar.Content title={t("product.addNew")} />
      </Appbar.Header>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              {t("product.details")}
            </Text>

            {barcode && (
              <Controller
                control={control}
                name="barcode"
                render={({ field: { value } }) => (
                  <TextInput
                    label={t("product.barcodeLabel")}
                    value={value}
                    editable={false}
                    style={styles.input}
                    left={<TextInput.Icon icon="barcode" />}
                  />
                )}
              />
            )}

            <Controller
              control={control}
              name="name"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  label={t("common.name")}
                  value={value}
                  onBlur={onBlur}
                  onChangeText={onChange}
                  error={!!errors.name}
                  style={styles.input}
                  left={<TextInput.Icon icon="tag" />}
                />
              )}
            />
            {errors.name && (
              <Text style={styles.errorText}>{errors.name.message}</Text>
            )}

            <Controller
              control={control}
              name="brand"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  label={t("common.brand")}
                  value={value}
                  onBlur={onBlur}
                  onChangeText={onChange}
                  error={!!errors.brand}
                  style={styles.input}
                  left={<TextInput.Icon icon="factory" />}
                />
              )}
            />

            <View style={styles.categoryContainer}>
              <Text variant="bodyMedium" style={styles.categoryLabel}>
                {t("common.category")}
              </Text>
              <Menu
                visible={categoryMenuVisible}
                onDismiss={() => setCategoryMenuVisible(false)}
                anchor={
                  <Chip
                    mode="outlined"
                    onPress={() => setCategoryMenuVisible(true)}
                    style={styles.categoryChip}
                  >
                    {t(`categories.${selectedCategory}`)}
                  </Chip>
                }
              >
                {Object.values(ProductCategory).map((category) => (
                  <Menu.Item
                    key={category}
                    onPress={() => handleCategorySelect(category)}
                    title={t(`categories.${category}`)}
                    leadingIcon={
                      selectedCategory === category ? "check" : undefined
                    }
                  />
                ))}
              </Menu>
            </View>

            <Controller
              control={control}
              name="description"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  label={t("common.description")}
                  value={value}
                  onBlur={onBlur}
                  onChangeText={onChange}
                  multiline
                  numberOfLines={3}
                  style={styles.input}
                  left={<TextInput.Icon icon="text" />}
                />
              )}
            />

            <Controller
              control={control}
              name="quantity"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  label={t("common.quantity")}
                  value={value?.toString()}
                  onBlur={onBlur}
                  onChangeText={(text) => onChange(parseFloat(text) || 0)}
                  keyboardType="numeric"
                  error={!!errors.quantity}
                  style={styles.input}
                  left={<TextInput.Icon icon="counter" />}
                />
              )}
            />
            {errors.quantity && (
              <Text style={styles.errorText}>{errors.quantity.message}</Text>
            )}

            <Controller
              control={control}
              name="imageUrl"
              render={({ field: { onChange, onBlur, value } }) => (
                <TextInput
                  label="Image URL (optional)"
                  value={value}
                  onBlur={onBlur}
                  onChangeText={onChange}
                  error={!!errors.imageUrl}
                  style={styles.input}
                  left={<TextInput.Icon icon="image" />}
                />
              )}
            />
          </Card.Content>
        </Card>

        <View style={styles.buttonContainer}>
          <Button
            mode="contained"
            onPress={handleSubmit(onSubmit)}
            loading={isSubmitting}
            disabled={isSubmitting}
            style={styles.submitButton}
          >
            {t("product.addNew")}
          </Button>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F7F7F7",
  },
  header: {
    paddingHorizontal: spacing.sm,
  },
  content: {
    flex: 1,
  },
  card: {
    margin: spacing.md,
    borderRadius: 12,
  },
  sectionTitle: {
    marginBottom: spacing.md,
    fontWeight: "bold",
  },
  input: {
    marginBottom: spacing.sm,
  },
  categoryContainer: {
    marginBottom: spacing.md,
  },
  categoryLabel: {
    marginBottom: spacing.sm,
    color: "#666",
  },
  categoryChip: {
    alignSelf: "flex-start",
  },

  buttonContainer: {
    padding: spacing.md,
  },
  submitButton: {
    paddingVertical: spacing.sm,
  },
  errorText: {
    color: "#F44336",
    fontSize: 12,
    marginTop: -spacing.sm,
    marginBottom: spacing.sm,
  },
});
