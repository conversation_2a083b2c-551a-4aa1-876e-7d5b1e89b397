import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Alert, Image, ScrollView, StyleSheet, View } from "react-native";
import {
  Button,
  Card,
  Chip,
  Dialog,
  IconButton,
  Portal,
  Text,
  TextInput,
} from "react-native-paper";
import { SafeAreaView } from "react-native-safe-area-context";

import { borderRadius, spacing } from "@/constants/theme";
import { useErrorHandling } from "@/hooks";
import { usePantryStore } from "@/stores";
import { Product } from "@/types";
import { formatProductQuantity } from "@/utils";

export default function ProductDetailsScreen() {
  const { t } = useTranslation();
  const { withErrorHandling } = useErrorHandling();
  const { id } = useLocalSearchParams<{ id: string }>();

  const { products, updateQuantity, removeProduct } = usePantryStore();

  const [product, setProduct] = useState<Product | null>(null);
  const [quantityDialogVisible, setQuantityDialogVisible] = useState(false);
  const [newQuantity, setNewQuantity] = useState("");

  useEffect(() => {
    const foundProduct = products.find((p) => p.id === id);
    setProduct(foundProduct || null);
  }, [id, products]);

  const handleQuantityChange = (change: number) => {
    if (!product) return;

    const newQty = Math.max(0, product.quantity + change);
    if (newQty === 0) {
      handleDelete();
    } else {
      withErrorHandling(async () => {
        updateQuantity(product.id, newQty);
      });
    }
  };

  const handleQuantityEdit = () => {
    if (!product) return;
    setNewQuantity(product.quantity.toString());
    setQuantityDialogVisible(true);
  };

  const handleQuantitySubmit = () => {
    if (!product) return;

    const qty = parseFloat(newQuantity);
    if (isNaN(qty) || qty < 0) return;

    if (qty === 0) {
      setQuantityDialogVisible(false);
      handleDelete();
    } else {
      withErrorHandling(async () => {
        updateQuantity(product.id, qty);
        setQuantityDialogVisible(false);
      });
    }
  };

  const handleDelete = () => {
    if (!product) return;

    Alert.alert(t("product.confirmDelete"), t("product.deleteConfirmation"), [
      {
        text: t("common.cancel"),
        style: "cancel",
      },
      {
        text: t("common.delete"),
        style: "destructive",
        onPress: () => {
          withErrorHandling(async () => {
            removeProduct(product.id);
            router.back();
          });
        },
      },
    ]);
  };

  if (!product) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.centerContainer}>
          <Text variant="bodyLarge">Product not found</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <Card style={styles.card}>
          <Card.Content>
            {product.imageUrl && (
              <Image source={{ uri: product.imageUrl }} style={styles.image} />
            )}

            <Text variant="headlineMedium" style={styles.productName}>
              {product.name}
            </Text>

            {product.brand && (
              <Text variant="titleMedium" style={styles.brand}>
                {product.brand}
              </Text>
            )}

            <View style={styles.categoryContainer}>
              <Chip mode="outlined" style={styles.categoryChip}>
                {t(`categories.${product.category}`)}
              </Chip>
              {product.isManuallyAdded && (
                <Chip mode="outlined" style={styles.manualChip}>
                  Manual
                </Chip>
              )}
            </View>

            {product.description && (
              <Text variant="bodyMedium" style={styles.description}>
                {product.description}
              </Text>
            )}

            {product.barcode && (
              <View style={styles.barcodeContainer}>
                <Text variant="bodySmall" style={styles.barcodeLabel}>
                  {t("product.barcodeLabel")}:
                </Text>
                <Text variant="bodyMedium" style={styles.barcode}>
                  {product.barcode}
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Quantity Management
            </Text>

            <View style={styles.quantityContainer}>
              <IconButton
                icon="minus"
                size={24}
                onPress={() => handleQuantityChange(-1)}
                style={styles.quantityButton}
              />

              <Button
                mode="outlined"
                onPress={handleQuantityEdit}
                style={styles.quantityDisplay}
              >
                {formatProductQuantity(product.quantity)}
              </Button>

              <IconButton
                icon="plus"
                size={24}
                onPress={() => handleQuantityChange(1)}
                style={styles.quantityButton}
              />
            </View>
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <Card.Content>
            <Text variant="titleMedium" style={styles.sectionTitle}>
              Information
            </Text>

            <View style={styles.infoRow}>
              <Text variant="bodyMedium" style={styles.infoLabel}>
                Added:
              </Text>
              <Text variant="bodyMedium">
                {new Date(product.addedAt).toLocaleDateString()}
              </Text>
            </View>

            <View style={styles.infoRow}>
              <Text variant="bodyMedium" style={styles.infoLabel}>
                Last Updated:
              </Text>
              <Text variant="bodyMedium">
                {new Date(product.updatedAt).toLocaleDateString()}
              </Text>
            </View>

            {product.expiryDate && (
              <View style={styles.infoRow}>
                <Text variant="bodyMedium" style={styles.infoLabel}>
                  Expires:
                </Text>
                <Text variant="bodyMedium" style={styles.expiryDate}>
                  {new Date(product.expiryDate).toLocaleDateString()}
                </Text>
              </View>
            )}
          </Card.Content>
        </Card>
      </ScrollView>

      <Portal>
        <Dialog
          visible={quantityDialogVisible}
          onDismiss={() => setQuantityDialogVisible(false)}
        >
          <Dialog.Title>Edit Quantity</Dialog.Title>
          <Dialog.Content>
            <TextInput
              label={t("common.quantity")}
              value={newQuantity}
              onChangeText={setNewQuantity}
              keyboardType="numeric"
              autoFocus
            />
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setQuantityDialogVisible(false)}>
              {t("common.cancel")}
            </Button>
            <Button onPress={handleQuantitySubmit}>{t("common.save")}</Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F7F7F7",
  },
  content: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  card: {
    margin: spacing.md,
    borderRadius: borderRadius.lg,
  },
  image: {
    width: "100%",
    height: 200,
    borderRadius: borderRadius.md,
    marginBottom: spacing.md,
  },
  productName: {
    fontWeight: "bold",
    marginBottom: spacing.sm,
  },
  brand: {
    color: "#666",
    marginBottom: spacing.md,
  },
  categoryContainer: {
    flexDirection: "row",
    gap: spacing.sm,
    marginBottom: spacing.md,
  },
  categoryChip: {
    height: 32,
  },
  manualChip: {
    height: 32,
    backgroundColor: "#E3F2FD",
  },
  description: {
    lineHeight: 22,
    marginBottom: spacing.md,
  },
  barcodeContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: spacing.sm,
  },
  barcodeLabel: {
    color: "#666",
  },
  barcode: {
    fontFamily: "monospace",
  },
  sectionTitle: {
    fontWeight: "bold",
    marginBottom: spacing.md,
  },
  quantityContainer: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    gap: spacing.md,
  },
  quantityButton: {
    backgroundColor: "#FEA405",
  },
  quantityDisplay: {
    minWidth: 120,
  },
  infoRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: spacing.sm,
  },
  infoLabel: {
    color: "#666",
    fontWeight: "500",
  },
  expiryDate: {
    color: "#F44336",
  },
});
