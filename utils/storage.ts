import AsyncStorage from '@react-native-async-storage/async-storage';
import { Product } from '@/types';
import { ErrorStorageError } from '@/errors';

const STORAGE_KEYS = {
  PRODUCTS: '@pantry_products',
  SETTINGS: '@pantry_settings',
  USER_LANGUAGE: '@user_language',
} as const;

export class StorageService {
  // Products
  static async saveProducts(products: Product[]): Promise<void> {
    try {
      const jsonValue = JSON.stringify(products);
      await AsyncStorage.setItem(STORAGE_KEYS.PRODUCTS, jsonValue);
    } catch (error) {
      throw new ErrorStorageError(error);
    }
  }

  static async loadProducts(): Promise<Product[]> {
    try {
      const jsonValue = await AsyncStorage.getItem(STORAGE_KEYS.PRODUCTS);
      return jsonValue != null ? JSON.parse(jsonValue) : [];
    } catch (error) {
      throw new ErrorStorageError(error);
    }
  }

  static async addProduct(product: Product): Promise<void> {
    try {
      const products = await this.loadProducts();
      products.push(product);
      await this.saveProducts(products);
    } catch (error) {
      throw new ErrorStorageError(error);
    }
  }

  static async updateProduct(updatedProduct: Product): Promise<void> {
    try {
      const products = await this.loadProducts();
      const index = products.findIndex(p => p.id === updatedProduct.id);
      if (index !== -1) {
        products[index] = updatedProduct;
        await this.saveProducts(products);
      }
    } catch (error) {
      throw new ErrorStorageError(error);
    }
  }

  static async removeProduct(productId: string): Promise<void> {
    try {
      const products = await this.loadProducts();
      const filteredProducts = products.filter(p => p.id !== productId);
      await this.saveProducts(filteredProducts);
    } catch (error) {
      throw new ErrorStorageError(error);
    }
  }

  static async clearAllProducts(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.PRODUCTS);
    } catch (error) {
      throw new ErrorStorageError(error);
    }
  }

  // Settings
  static async saveSettings(settings: any): Promise<void> {
    try {
      const jsonValue = JSON.stringify(settings);
      await AsyncStorage.setItem(STORAGE_KEYS.SETTINGS, jsonValue);
    } catch (error) {
      throw new ErrorStorageError(error);
    }
  }

  static async loadSettings(): Promise<any> {
    try {
      const jsonValue = await AsyncStorage.getItem(STORAGE_KEYS.SETTINGS);
      return jsonValue != null ? JSON.parse(jsonValue) : null;
    } catch (error) {
      throw new ErrorStorageError(error);
    }
  }

  // Language
  static async saveLanguage(language: string): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.USER_LANGUAGE, language);
    } catch (error) {
      throw new ErrorStorageError(error);
    }
  }

  static async loadLanguage(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(STORAGE_KEYS.USER_LANGUAGE);
    } catch (error) {
      throw new ErrorStorageError(error);
    }
  }

  // Clear all data
  static async clearAllData(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        STORAGE_KEYS.PRODUCTS,
        STORAGE_KEYS.SETTINGS,
      ]);
    } catch (error) {
      throw new ErrorStorageError(error);
    }
  }
}
