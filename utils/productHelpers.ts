import { ProductCategory } from "@/enums";
import { CreateProductInput, ProductApiResponse } from "@/types";

export const mapApiResponseToProduct = (
  apiResponse: ProductApiResponse,
  barcode: string
): CreateProductInput => {
  const product = apiResponse.product;

  // Determine category based on categories string
  const getCategoryFromString = (
    categoriesString?: string
  ): ProductCategory => {
    if (!categoriesString) return ProductCategory.OTHER;

    const categories = categoriesString.toLowerCase();

    if (categories.includes("beverage") || categories.includes("drink")) {
      return ProductCategory.BEVERAGES;
    }
    if (
      categories.includes("food") ||
      categories.includes("snack") ||
      categories.includes("meal") ||
      categories.includes("dairy")
    ) {
      return ProductCategory.FOOD;
    }
    if (
      categories.includes("personal care") ||
      categories.includes("cosmetic") ||
      categories.includes("hygiene")
    ) {
      return ProductCategory.PERSONAL_CARE;
    }
    if (categories.includes("household") || categories.includes("cleaning")) {
      return ProductCategory.HOUSEHOLD;
    }

    return ProductCategory.OTHER;
  };

  return {
    barcode,
    name: product.product_name || "Unknown Product",
    brand: product.brands || undefined,
    category: getCategoryFromString(product.categories),
    imageUrl: product.image_url || undefined,
    description: product.generic_name || undefined,
    quantity: 1,
  };
};

export const formatProductQuantity = (quantity: number): string => {
  return `${quantity} pcs`;
};

export const isValidBarcode = (barcode: string): boolean => {
  // Basic barcode validation - should be numeric and have appropriate length
  const numericBarcode = barcode.replace(/\D/g, "");
  return numericBarcode.length >= 8 && numericBarcode.length <= 14;
};
