{"expo": {"name": "BarcodePantry", "slug": "barcode-pantry", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "barcodepantry", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#FEA405"}, "ios": {"supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "This app uses the camera to scan barcodes for adding products to your pantry."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#FEA405"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.CAMERA"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera to scan barcodes."}], ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#FEA405"}]], "experiments": {"typedRoutes": true}}}